#!/usr/bin/env python3
"""
Database Migration Script
Adds missing overtime columns to existing attendance table
"""

import sqlite3
import os
from database import get_db

def migrate_database():
    """Add missing columns to existing database"""
    print("🔄 Starting database migration...")
    
    # Connect to database
    db = sqlite3.connect('vishnorex.db')
    cursor = db.cursor()
    
    try:
        # Check if overtime columns exist
        cursor.execute("PRAGMA table_info(attendance)")
        columns = [col[1] for col in cursor.fetchall()]
        
        print(f"📋 Current attendance table columns: {columns}")
        
        # Add overtime_in column if it doesn't exist
        if 'overtime_in' not in columns:
            print("➕ Adding overtime_in column...")
            cursor.execute("ALTER TABLE attendance ADD COLUMN overtime_in TIME")
            print("✅ overtime_in column added successfully")
        else:
            print("✅ overtime_in column already exists")
            
        # Add overtime_out column if it doesn't exist
        if 'overtime_out' not in columns:
            print("➕ Adding overtime_out column...")
            cursor.execute("ALTER TABLE attendance ADD COLUMN overtime_out TIME")
            print("✅ overtime_out column added successfully")
        else:
            print("✅ overtime_out column already exists")
            
        # Commit changes
        db.commit()
        
        # Verify the changes
        cursor.execute("PRAGMA table_info(attendance)")
        new_columns = [col[1] for col in cursor.fetchall()]
        print(f"📋 Updated attendance table columns: {new_columns}")
        
        print("🎉 Database migration completed successfully!")
        
    except Exception as e:
        print(f"❌ Migration failed: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()

def check_database_schema():
    """Check current database schema"""
    print("🔍 Checking database schema...")
    
    db = sqlite3.connect('vishnorex.db')
    cursor = db.cursor()
    
    try:
        # Check attendance table structure
        cursor.execute("PRAGMA table_info(attendance)")
        attendance_columns = cursor.fetchall()
        
        print("\n📊 Attendance Table Schema:")
        for col in attendance_columns:
            print(f"  - {col[1]} ({col[2]})")
            
        # Check if overtime columns exist
        column_names = [col[1] for col in attendance_columns]
        
        if 'overtime_in' in column_names and 'overtime_out' in column_names:
            print("✅ All required columns present")
            return True
        else:
            print("❌ Missing overtime columns")
            return False
            
    except Exception as e:
        print(f"❌ Schema check failed: {str(e)}")
        return False
    finally:
        db.close()

if __name__ == "__main__":
    print("🚀 Database Migration Tool")
    print("=" * 50)
    
    # Check if database file exists
    if not os.path.exists('vishnorex.db'):
        print("❌ Database file 'vishnorex.db' not found!")
        print("Please run the application first to create the database.")
        exit(1)
    
    # Check current schema
    if not check_database_schema():
        print("\n🔧 Running migration...")
        migrate_database()
    else:
        print("\n✅ Database schema is up to date!")
    
    print("\n🎯 Migration complete. You can now restart your application.")

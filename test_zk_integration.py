#!/usr/bin/env python3
"""
Test script for ZK biometric device integration
"""

from zk_biometric import ZKBiometricDevice, sync_attendance_from_device
import datetime

def test_zk_connection():
    """Test basic ZK device connection"""
    print("Testing ZK Biometric Device Integration")
    print("=" * 50)
    
    device_ip = '*************'
    print(f"Testing connection to device at {device_ip}")
    
    # Initialize device
    zk_device = ZKBiometricDevice(device_ip)
    
    try:
        # Test connection
        if zk_device.connect():
            print("✅ Connection successful!")
            
            # Get device info
            users = zk_device.get_users()
            print(f"📊 Device has {len(users)} registered users")
            
            if users:
                print("\n👥 Sample users:")
                for i, user in enumerate(users[:5]):  # Show first 5 users
                    print(f"   {i+1}. ID: {user['user_id']}, Name: {user['name'] or 'N/A'}")
            
            # Get attendance records
            attendance = zk_device.get_attendance_records()
            print(f"📋 Device has {len(attendance)} attendance records")
            
            if attendance:
                print("\n🕐 Recent attendance records:")
                # Sort by timestamp and show last 5
                sorted_attendance = sorted(attendance, key=lambda x: x['timestamp'], reverse=True)
                for i, record in enumerate(sorted_attendance[:5]):
                    punch_type = {0: 'Check In', 1: 'Check Out', 2: 'Break Out', 3: 'Break In'}.get(record['punch'], 'Unknown')
                    print(f"   {i+1}. User: {record['user_id']}, Time: {record['timestamp']}, Type: {punch_type}")
            
            zk_device.disconnect()
            print("\n✅ Test completed successfully!")
            return True
            
        else:
            print("❌ Failed to connect to device")
            print("Please check:")
            print("   - Device is powered on")
            print("   - Network connection is working")
            print("   - IP address is correct (*************)")
            print("   - Device is not being used by another application")
            return False
            
    except Exception as e:
        print(f"❌ Error during test: {str(e)}")
        return False

def test_sync_functionality():
    """Test attendance synchronization"""
    print("\n" + "=" * 50)
    print("Testing Attendance Synchronization")
    print("=" * 50)
    
    try:
        # Note: This requires Flask app context, so we'll simulate it
        print("🔄 Testing sync functionality...")
        
        # This would normally sync with the Flask app
        # result = sync_attendance_from_device('*************', 1)
        # print(f"Sync result: {result}")
        
        print("ℹ️  Sync test requires Flask app context")
        print("   Use the web interface to test sync functionality")
        
        return True
        
    except Exception as e:
        print(f"❌ Sync test error: {str(e)}")
        return False

def test_mysql_connection():
    """Test MySQL database connection"""
    print("\n" + "=" * 50)
    print("Testing MySQL Connection")
    print("=" * 50)
    
    try:
        import pymysql
        
        # Test connection with default settings
        config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'yourpass',
            'database': 'staff'
        }
        
        print(f"Testing MySQL connection to {config['host']}")
        
        try:
            conn = pymysql.connect(**config)
            cursor = conn.cursor()
            
            # Test query
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"✅ MySQL connection successful!")
            print(f"   MySQL version: {version[0] if version else 'Unknown'}")
            
            # Check if attendance_log table exists
            cursor.execute("SHOW TABLES LIKE 'attendance_log'")
            table_exists = cursor.fetchone()
            
            if table_exists:
                print("✅ attendance_log table exists")
                cursor.execute("SELECT COUNT(*) FROM attendance_log")
                count = cursor.fetchone()
                print(f"   Table has {count[0] if count else 0} records")
            else:
                print("ℹ️  attendance_log table will be created on first sync")
            
            cursor.close()
            conn.close()
            return True
            
        except pymysql.Error as e:
            print(f"❌ MySQL connection failed: {str(e)}")
            print("Please check:")
            print("   - MySQL server is running")
            print("   - Username and password are correct")
            print("   - Database 'staff' exists")
            print("   - User has proper permissions")
            return False
            
    except ImportError:
        print("❌ PyMySQL not installed")
        return False

def main():
    """Run all tests"""
    print("ZK Biometric Device Integration Test Suite")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 3
    
    # Test 1: ZK Device Connection
    if test_zk_connection():
        tests_passed += 1
    
    # Test 2: Sync Functionality
    if test_sync_functionality():
        tests_passed += 1
    
    # Test 3: MySQL Connection
    if test_mysql_connection():
        tests_passed += 1
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"Tests passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! ZK integration is ready to use.")
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
    
    print("\nNext steps:")
    print("1. Start the Flask application: python app.py")
    print("2. Login as admin and go to 'Biometric Device' in the dashboard")
    print("3. Test connection and sync attendance data")

if __name__ == '__main__':
    main()

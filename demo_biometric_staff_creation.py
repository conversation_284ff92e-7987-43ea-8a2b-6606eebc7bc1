#!/usr/bin/env python3
"""
Demo script showing the biometric staff creation workflow
This demonstrates the complete process without requiring web authentication
"""

from zk_biometric import ZKBiometricDevice
import time

def demo_biometric_staff_creation():
    """Demonstrate the biometric staff creation workflow"""
    print("🎯 Biometric Staff Creation Demo")
    print("=" * 50)
    
    # Demo staff data
    staff_data = {
        'staff_id': 'DEMO001',
        'full_name': 'Demo Staff Member',
        'department': 'IT',
        'position': 'Software Engineer'
    }
    
    device_ip = '*************'
    
    print(f"👤 Demo Staff: {staff_data['full_name']} (ID: {staff_data['staff_id']})")
    print(f"🌐 Device IP: {device_ip}")
    print()
    
    # Initialize ZK device
    print("🔌 Step 1: Connecting to ZK biometric device...")
    zk_device = ZKBiometricDevice(device_ip)
    
    try:
        if not zk_device.connect():
            print("❌ Failed to connect to ZK device")
            print("💡 Please check:")
            print("   - Device is powered on")
            print("   - Network connection is working")
            print("   - Device IP is correct (*************)")
            return False
        
        print("✅ Connected to ZK device successfully!")
        
        # Check current users
        print("\n📊 Step 2: Checking current device users...")
        users = zk_device.get_users()
        print(f"   Device currently has {len(users)} registered users")
        
        # Check if demo user already exists
        demo_user_exists = any(user['user_id'] == staff_data['staff_id'] for user in users)
        if demo_user_exists:
            print(f"   ⚠️  User {staff_data['staff_id']} already exists on device")
            print("   🔄 Deleting existing user for demo...")
            zk_device.delete_user(staff_data['staff_id'])
        
        # Step 3: Enroll user on device
        print(f"\n👤 Step 3: Enrolling {staff_data['full_name']} on device...")
        success = zk_device.enroll_user(
            user_id=staff_data['staff_id'],
            name=staff_data['full_name'],
            privilege=0  # Normal user
        )
        
        if success:
            print("✅ User enrolled on device successfully!")
        else:
            print("❌ Failed to enroll user on device")
            zk_device.disconnect()
            return False
        
        # Step 4: Start enrollment mode
        print("\n🔧 Step 4: Starting biometric enrollment mode...")
        if zk_device.start_enrollment_mode():
            print("✅ Device is now in enrollment mode")
            print()
            print("🖐️  MANUAL STEP REQUIRED:")
            print("   Please ask the staff member to:")
            print("   1. Place their finger on the biometric scanner")
            print("   2. Follow the device prompts for fingerprint capture")
            print("   3. Complete the enrollment process")
            print()
            
            # Wait for user input
            input("   Press Enter when biometric enrollment is complete...")
            
            # End enrollment mode
            print("\n🔓 Step 5: Ending enrollment mode...")
            if zk_device.end_enrollment_mode():
                print("✅ Device returned to normal operation mode")
            else:
                print("⚠️  Warning: Could not end enrollment mode")
        else:
            print("❌ Failed to start enrollment mode")
        
        # Step 6: Verify enrollment
        print("\n✅ Step 6: Verifying biometric enrollment...")
        updated_users = zk_device.get_users()
        enrolled_user = None
        
        for user in updated_users:
            if user['user_id'] == staff_data['staff_id']:
                enrolled_user = user
                break
        
        if enrolled_user:
            print(f"✅ User {staff_data['staff_id']} found on device")
            print(f"   Name: {enrolled_user['name']}")
            print(f"   Privilege: {enrolled_user['privilege']}")
            print("✅ Biometric enrollment verified!")
        else:
            print(f"❌ User {staff_data['staff_id']} not found on device")
            print("   Biometric enrollment may not be complete")
        
        # Disconnect from device
        zk_device.disconnect()
        print("\n🔌 Disconnected from ZK device")
        
        # Summary
        print("\n" + "=" * 50)
        print("📋 ENROLLMENT SUMMARY")
        print("=" * 50)
        
        if enrolled_user:
            print("🎉 SUCCESS: Biometric enrollment completed!")
            print(f"   Staff ID: {staff_data['staff_id']}")
            print(f"   Name: {staff_data['full_name']}")
            print(f"   Device Status: Enrolled")
            print()
            print("✅ This staff member can now:")
            print("   - Use biometric authentication for attendance")
            print("   - Be created in the staff management system")
            print("   - Access the system using their biometric data")
        else:
            print("⚠️  INCOMPLETE: Biometric enrollment not verified")
            print("   Please retry the enrollment process")
        
        print("\n🌐 WEB INTERFACE WORKFLOW:")
        print("1. Open: http://127.0.0.1:5000")
        print("2. Login as admin: school_admin / admin123")
        print("3. Click 'Add Staff'")
        print("4. Fill staff details")
        print("5. Click 'Next: Biometric Enrollment'")
        print("6. Click 'Start Enrollment'")
        print("7. Complete biometric capture")
        print("8. Click 'Verify Enrollment'")
        print("9. Click 'Create Staff Account'")
        
        return enrolled_user is not None
        
    except Exception as e:
        print(f"❌ Error during demo: {str(e)}")
        try:
            zk_device.disconnect()
        except:
            pass
        return False

def show_workflow_diagram():
    """Show the complete workflow diagram"""
    print("\n📊 BIOMETRIC STAFF CREATION WORKFLOW")
    print("=" * 60)
    print()
    print("┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐")
    print("│   Admin Opens   │    │  Fill Staff     │    │  Biometric      │")
    print("│   Add Staff     │───▶│  Details Form   │───▶│  Enrollment     │")
    print("│   Modal         │    │                 │    │  Required       │")
    print("└─────────────────┘    └─────────────────┘    └─────────────────┘")
    print("                                                       │")
    print("                                                       ▼")
    print("┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐")
    print("│  Staff Account  │    │  Verify         │    │  Start Device   │")
    print("│  Created with   │◀───│  Biometric      │◀───│  Enrollment     │")
    print("│  Biometric ID   │    │  Capture        │    │  Mode           │")
    print("└─────────────────┘    └─────────────────┘    └─────────────────┘")
    print()
    print("🔒 SECURITY FEATURES:")
    print("• Biometric enrollment is mandatory")
    print("• Staff cannot be created without biometric data")
    print("• Device verification ensures data integrity")
    print("• Real-time enrollment status tracking")

if __name__ == '__main__':
    print("VishnoRex Biometric Staff Creation Demo")
    print("=" * 60)
    
    show_workflow_diagram()
    
    print("\n🚀 Starting demo...")
    response = input("Continue with biometric enrollment demo? (y/n): ").lower()
    
    if response == 'y':
        success = demo_biometric_staff_creation()
        
        if success:
            print("\n🎉 Demo completed successfully!")
        else:
            print("\n⚠️  Demo encountered issues. Please check device connection.")
    else:
        print("Demo cancelled.")
    
    print("\n💡 TIP: Use the web interface for the complete user experience!")
    print("   http://127.0.0.1:5000")

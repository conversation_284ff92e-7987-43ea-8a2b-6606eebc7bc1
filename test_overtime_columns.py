#!/usr/bin/env python3
"""
Test script to verify overtime columns are working correctly
"""

import sqlite3
import datetime

def test_overtime_columns():
    """Test that overtime columns can be used properly"""
    print("🧪 Testing overtime columns functionality...")
    
    # Connect to database
    db = sqlite3.connect('vishnorex.db')
    cursor = db.cursor()
    
    try:
        # Test 1: Check if columns exist
        print("\n📋 Test 1: Checking column existence...")
        cursor.execute("PRAGMA table_info(attendance)")
        columns = [col[1] for col in cursor.fetchall()]
        
        required_columns = ['overtime_in', 'overtime_out']
        for col in required_columns:
            if col in columns:
                print(f"✅ {col} column exists")
            else:
                print(f"❌ {col} column missing")
                return False
        
        # Test 2: Try to insert a record with overtime data
        print("\n📝 Test 2: Testing insert with overtime data...")
        test_data = {
            'staff_id': 999,  # Test staff ID
            'school_id': 1,
            'date': datetime.date.today(),
            'time_in': '09:00:00',
            'time_out': '17:00:00',
            'overtime_in': '18:00:00',
            'overtime_out': '20:00:00',
            'status': 'present'
        }
        
        # Delete any existing test record
        cursor.execute("DELETE FROM attendance WHERE staff_id = ?", (test_data['staff_id'],))
        
        # Insert test record
        cursor.execute('''
            INSERT INTO attendance (staff_id, school_id, date, time_in, time_out, overtime_in, overtime_out, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            test_data['staff_id'], test_data['school_id'], test_data['date'],
            test_data['time_in'], test_data['time_out'], 
            test_data['overtime_in'], test_data['overtime_out'], test_data['status']
        ))
        
        print("✅ Insert with overtime data successful")
        
        # Test 3: Try to query the overtime data
        print("\n🔍 Test 3: Testing query with overtime columns...")
        cursor.execute('''
            SELECT staff_id, date, time_in, time_out, overtime_in, overtime_out, status
            FROM attendance 
            WHERE staff_id = ?
        ''', (test_data['staff_id'],))
        
        result = cursor.fetchone()
        if result:
            print("✅ Query with overtime columns successful")
            print(f"   📊 Data: {dict(zip(['staff_id', 'date', 'time_in', 'time_out', 'overtime_in', 'overtime_out', 'status'], result))}")
        else:
            print("❌ Query failed - no data returned")
            return False
        
        # Test 4: Test UPDATE operations on overtime columns
        print("\n🔄 Test 4: Testing update operations...")
        cursor.execute('''
            UPDATE attendance 
            SET overtime_in = ?, overtime_out = ?
            WHERE staff_id = ?
        ''', ('19:00:00', '21:00:00', test_data['staff_id']))
        
        # Verify update
        cursor.execute('''
            SELECT overtime_in, overtime_out 
            FROM attendance 
            WHERE staff_id = ?
        ''', (test_data['staff_id'],))
        
        updated_result = cursor.fetchone()
        if updated_result and updated_result[0] == '19:00:00' and updated_result[1] == '21:00:00':
            print("✅ Update operations successful")
        else:
            print("❌ Update operations failed")
            return False
        
        # Clean up test data
        cursor.execute("DELETE FROM attendance WHERE staff_id = ?", (test_data['staff_id'],))
        db.commit()
        
        print("\n🎉 All tests passed! Overtime columns are working correctly.")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        return False
    finally:
        db.close()

def test_admin_dashboard_query():
    """Test the specific query that was failing in admin dashboard"""
    print("\n🔍 Testing admin dashboard query...")
    
    db = sqlite3.connect('vishnorex.db')
    cursor = db.cursor()
    
    try:
        # This is the query from admin_dashboard function that was failing
        today = datetime.date.today()
        cursor.execute('''
            SELECT s.id as staff_id, s.staff_id as staff_number, s.full_name, s.department,
                   a.time_in, a.time_out, a.overtime_in, a.overtime_out, 
                   COALESCE(a.status, 'absent') as status
            FROM staff s
            LEFT JOIN attendance a ON s.id = a.staff_id AND a.date = ?
            WHERE s.school_id = ?
            ORDER BY s.full_name
        ''', (today, 1))
        
        results = cursor.fetchall()
        print(f"✅ Admin dashboard query successful - returned {len(results)} records")
        
        if results:
            print("📊 Sample record structure:")
            columns = ['staff_id', 'staff_number', 'full_name', 'department', 'time_in', 'time_out', 'overtime_in', 'overtime_out', 'status']
            sample = dict(zip(columns, results[0]))
            for key, value in sample.items():
                print(f"   {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Admin dashboard query failed: {str(e)}")
        return False
    finally:
        db.close()

if __name__ == "__main__":
    print("🚀 Overtime Columns Test Suite")
    print("=" * 50)
    
    # Run tests
    test1_passed = test_overtime_columns()
    test2_passed = test_admin_dashboard_query()
    
    print("\n" + "=" * 50)
    if test1_passed and test2_passed:
        print("🎉 ALL TESTS PASSED! Database is ready for use.")
    else:
        print("❌ Some tests failed. Please check the errors above.")

/* General Styles */
body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.card {
    border-radius: 10px;
    overflow: hidden;
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
/* Header Styles */
header {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

/* Logo Styles */
.logo-container {
    display: flex;
    align-items: center;
}

.logo-text {
    line-height: 1.2;
}

/* Utility Classes for Inline Styles */
.logo-height {
    height: 50px;
}

.search-width {
    width: 200px;
}

.hidden {
    display: none;
}

.fingerprint-placeholder {
    width: 200px;
    height: 200px;
    margin: 0 auto;
    background-color: #eee;
    border-radius: 50%;
}

.progress-bar-initial {
    width: 0%;
}

/* Staff Creation Progress */
.progress-thin {
    height: 3px;
}

.progress-step-50 {
    width: 50%;
}

/* Enrollment Progress */
.enrollment-progress-bar {
    width: 0%;
}

/* Report Button Styles */
#exportCompanyReportBtn {
    transition: all 0.3s ease;
}

#exportCompanyReportBtn:hover {
    background-color: #0d6efd;
    color: white;
}

/* Date Picker Styles */
.report-date-picker {
    max-width: 300px;
    margin: 0 auto;
}
.card-header {
    padding: 1rem 1.5rem;
}

.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Login Page Styles */
#loginForm {
    max-width: 400px;
    margin: 0 auto;
}

/* Dashboard Styles */
.attendance-status {
    font-size: 1.2rem;
    font-weight: bold;
}

.badge {
    font-weight: 500;
    padding: 0.5em 0.75em;
}

/* Custom Button Styles */
.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

.btn-success {
    background-color: #198754;
    border-color: #198754;
}

.btn-success:hover {
    background-color: #157347;
    border-color: #146c43;
}

.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
}

.btn-danger:hover {
    background-color: #bb2d3b;
    border-color: #b02a37;
}

/* Table Styles */
.table th {
    font-weight: 600;
    color: #495057;
    background-color: #f8f9fa;
}

/* Modal Styles */
.modal-header {
    padding: 1rem 1.5rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        overflow-x: auto;
    }
}
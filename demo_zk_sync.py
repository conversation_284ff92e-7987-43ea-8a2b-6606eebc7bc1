#!/usr/bin/env python3
"""
Demo script showing ZK biometric device integration
This script demonstrates the complete workflow
"""

from zk import ZK, const
import pymysql
import sqlite3
import datetime
from app import app
from database import get_db

def demo_zk_integration():
    """Demonstrate the complete ZK integration workflow"""
    print("🎯 ZK Biometric Device Integration Demo")
    print("=" * 60)
    
    # Device configuration
    device_ip = '*************'
    device_port = 4370
    
    print(f"📡 Connecting to ZK device at {device_ip}:{device_port}")
    
    # Initialize ZK connection
    zk = ZK(device_ip, port=device_port, timeout=5)
    
    try:
        # Step 1: Connect to device
        conn = zk.connect()
        print("✅ Connected to ZK device successfully!")
        
        # Step 2: Disable device during data reading
        conn.disable_device()
        print("🔒 Device locked for data reading")
        
        # Step 3: Get attendance records
        print("📋 Retrieving attendance records...")
        attendance = conn.get_attendance()
        print(f"   Found {len(attendance)} attendance records")
        
        # Step 4: Process and display sample records
        if attendance:
            print("\n📊 Sample attendance records:")
            print("-" * 80)
            print(f"{'User ID':<10} {'Timestamp':<20} {'Punch Type':<12} {'Status'}")
            print("-" * 80)
            
            # Show last 10 records
            for i, record in enumerate(list(attendance)[-10:]):
                punch_type = {
                    0: 'Check In',
                    1: 'Check Out', 
                    2: 'Break Out',
                    3: 'Break In'
                }.get(record.punch, 'Unknown')
                
                print(f"{record.user_id:<10} {str(record.timestamp):<20} {punch_type:<12} {record.status}")
        
        # Step 5: Get users from device
        print(f"\n👥 Retrieving users from device...")
        users = conn.get_users()
        print(f"   Found {len(users)} registered users")
        
        if users:
            print("\n📋 Sample users:")
            print("-" * 60)
            print(f"{'User ID':<10} {'Name':<20} {'Privilege':<10} {'Group'}")
            print("-" * 60)
            
            # Show first 10 users
            for user in users[:10]:
                name = user.name or 'N/A'
                print(f"{user.user_id:<10} {name:<20} {user.privilege:<10} {user.group_id}")
        
        # Step 6: Re-enable device
        conn.enable_device()
        print("\n🔓 Device unlocked")
        
        # Step 7: Disconnect
        conn.disconnect()
        print("🔌 Disconnected from device")
        
        # Step 8: Demonstrate database sync
        print(f"\n💾 Database Sync Demonstration")
        print("-" * 40)
        
        # SQLite sync (main database)
        print("📝 Syncing to SQLite database...")
        sqlite_synced = sync_to_sqlite(attendance)
        print(f"   ✅ Synced {sqlite_synced} records to SQLite")
        
        # MySQL sync (backup database)
        print("📝 Syncing to MySQL database...")
        mysql_synced = sync_to_mysql(attendance)
        print(f"   ✅ Synced {mysql_synced} records to MySQL")
        
        # Summary
        print(f"\n🎉 Integration Demo Complete!")
        print(f"   Device Users: {len(users)}")
        print(f"   Attendance Records: {len(attendance)}")
        print(f"   SQLite Synced: {sqlite_synced}")
        print(f"   MySQL Synced: {mysql_synced}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during demo: {str(e)}")
        return False

def sync_to_sqlite(attendance_records):
    """Sync attendance to SQLite database"""
    synced_count = 0
    
    try:
        with app.app_context():
            db = get_db()
            
            for record in attendance_records:
                try:
                    # Map device user_id to staff record
                    staff = db.execute('''
                        SELECT id FROM staff WHERE staff_id = ?
                    ''', (str(record.user_id),)).fetchone()
                    
                    if not staff:
                        continue
                    
                    staff_id = staff['id']
                    date = record.timestamp.date()
                    time_val = record.timestamp.time()
                    
                    # Handle check-in
                    if record.punch == 0:  # Check In
                        existing = db.execute('''
                            SELECT id FROM attendance 
                            WHERE staff_id = ? AND date = ?
                        ''', (staff_id, date)).fetchone()
                        
                        if not existing:
                            status = 'late' if time_val > datetime.time(9, 0) else 'present'
                            
                            db.execute('''
                                INSERT INTO attendance (staff_id, school_id, date, time_in, status)
                                VALUES (?, ?, ?, ?, ?)
                            ''', (staff_id, 1, date, time_val, status))
                            synced_count += 1
                    
                    # Handle check-out
                    elif record.punch == 1:  # Check Out
                        db.execute('''
                            UPDATE attendance 
                            SET time_out = ?
                            WHERE staff_id = ? AND date = ?
                        ''', (time_val, staff_id, date))
                
                except Exception as e:
                    print(f"   ⚠️ Error syncing record: {e}")
                    continue
            
            db.commit()
            
    except Exception as e:
        print(f"   ❌ SQLite sync error: {e}")
    
    return synced_count

def sync_to_mysql(attendance_records):
    """Sync attendance to MySQL database"""
    synced_count = 0
    
    try:
        # MySQL connection
        conn_db = pymysql.connect(
            host='localhost',
            user='root',
            password='yourpass',
            database='staff'
        )
        cursor = conn_db.cursor()
        
        # Create table if not exists
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS attendance_log (
                id INT AUTO_INCREMENT PRIMARY KEY,
                staff_id VARCHAR(50) NOT NULL,
                timestamp DATETIME NOT NULL,
                status INT NOT NULL,
                punch_type INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE KEY unique_record (staff_id, timestamp, punch_type)
            )
        ''')
        
        for record in attendance_records:
            try:
                query = """
                    INSERT IGNORE INTO attendance_log (staff_id, timestamp, status, punch_type)
                    VALUES (%s, %s, %s, %s)
                """
                cursor.execute(query, (
                    str(record.user_id),
                    record.timestamp,
                    record.status,
                    record.punch
                ))
                if cursor.rowcount > 0:
                    synced_count += 1
                    
            except Exception as e:
                print(f"   ⚠️ Error syncing to MySQL: {e}")
                continue
        
        conn_db.commit()
        cursor.close()
        conn_db.close()
        
    except Exception as e:
        print(f"   ⚠️ MySQL not available: {e}")
    
    return synced_count

def show_network_info():
    """Show network configuration information"""
    print("\n🌐 Network Configuration")
    print("-" * 30)
    print("Device IP: *************")
    print("Subnet Mask: *************")
    print("Gateway: ***********")
    print("Port: 4370")
    print("\nTo configure your network:")
    print("Windows: netsh interface ip set address name=\"Ethernet\" static ************* ************* ***********")
    print("Linux: sudo ip addr add *************/24 dev eth0")

if __name__ == '__main__':
    print("VishnoRex ZK Biometric Integration Demo")
    print("=" * 50)
    
    # Show network info
    show_network_info()
    
    # Ask user if they want to proceed
    response = input("\nProceed with demo? (y/n): ").lower()
    
    if response == 'y':
        success = demo_zk_integration()
        
        if success:
            print("\n🎉 Demo completed successfully!")
            print("\nNext steps:")
            print("1. Access the web interface at http://127.0.0.1:5000")
            print("2. Login as admin (school_admin / admin123)")
            print("3. Click 'Biometric Device' to use the web interface")
        else:
            print("\n❌ Demo failed. Please check device connection.")
    else:
        print("Demo cancelled.")

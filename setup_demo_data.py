#!/usr/bin/env python3
"""
Setup demo data for the VishnoRex Staff Attendance System
"""

from app import app
from database import get_db
from werkzeug.security import generate_password_hash
import datetime

def setup_demo_data():
    """Create demo schools, admins, and staff for testing"""
    
    with app.app_context():
        db = get_db()
        
        # Create demo school
        db.execute('''
            INSERT OR IGNORE INTO schools (name, address, contact_email, contact_phone)
            VALUES (?, ?, ?, ?)
        ''', ('Demo High School', '123 Education St, Demo City', '<EMAIL>', '******-0123'))
        
        school_id = db.execute('SELECT id FROM schools WHERE name = ?', ('Demo High School',)).fetchone()['id']
        
        # Create company admin
        company_admin_password = generate_password_hash('admin123')
        db.execute('''
            INSERT OR IGNORE INTO company_admins (username, password, full_name, email)
            VALUES (?, ?, ?, ?)
        ''', ('company_admin', company_admin_password, 'Company Administrator', '<EMAIL>'))
        
        # Create school admin
        admin_password = generate_password_hash('admin123')
        db.execute('''
            INSERT OR IGNORE INTO admins (school_id, username, password, full_name, email)
            VALUES (?, ?, ?, ?, ?)
        ''', (school_id, 'school_admin', admin_password, 'School Administrator', '<EMAIL>'))
        
        # Create demo staff
        staff_password = generate_password_hash('staff123')
        staff_data = [
            ('STAFF001', 'John Doe', '<EMAIL>', '******-0101', 'Mathematics', 'Teacher'),
            ('STAFF002', 'Jane Smith', '<EMAIL>', '******-0102', 'English', 'Teacher'),
            ('STAFF003', 'Bob Johnson', '<EMAIL>', '******-0103', 'Science', 'Teacher'),
        ]
        
        for staff_id, full_name, email, phone, department, position in staff_data:
            db.execute('''
                INSERT OR IGNORE INTO staff (school_id, staff_id, password, full_name, email, phone, department, position)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (school_id, staff_id, staff_password, full_name, email, phone, department, position))
        
        db.commit()
        print("Demo data created successfully!")
        print("\nLogin credentials:")
        print("Company Admin: username='company_admin', password='admin123'")
        print("School Admin: username='school_admin', password='admin123'")
        print("Staff: staff_id='STAFF001/STAFF002/STAFF003', password='staff123'")

if __name__ == '__main__':
    setup_demo_data()

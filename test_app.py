#!/usr/bin/env python3
"""
Simple test script to verify the Flask application is working correctly.
"""

import unittest
import tempfile
import os
from app import app, init_db

class FlaskAppTestCase(unittest.TestCase):
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.db_fd, app.config['DATABASE'] = tempfile.mkstemp()
        app.config['TESTING'] = True
        self.app = app.test_client()
        
        with app.app_context():
            init_db(app)
    
    def tearDown(self):
        """Clean up after each test method."""
        os.close(self.db_fd)
        os.unlink(app.config['DATABASE'])
    
    def test_index_page(self):
        """Test that the index page loads successfully."""
        rv = self.app.get('/')
        self.assertEqual(rv.status_code, 200)
        self.assertIn(b'Staff Attendance System', rv.data)
    
    def test_company_login_page(self):
        """Test that the company login page loads successfully."""
        rv = self.app.get('/company_login')
        self.assertEqual(rv.status_code, 200)
    
    def test_csrf_protection(self):
        """Test that CSRF protection is working."""
        rv = self.app.post('/login', data={
            'school_id': '1',
            'username': 'invalid',
            'password': 'invalid'
        })
        # Should return 400 error due to missing CSRF token
        self.assertEqual(rv.status_code, 400)
        self.assertIn(b'CSRF token is missing', rv.data)

if __name__ == '__main__':
    unittest.main()

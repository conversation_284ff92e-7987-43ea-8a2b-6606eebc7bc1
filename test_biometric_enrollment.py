#!/usr/bin/env python3
"""
Test script for biometric enrollment during staff creation
"""

import requests
import json
import time

def test_biometric_enrollment():
    """Test the complete biometric enrollment workflow"""
    base_url = 'http://127.0.0.1:5000'
    
    print("🧪 Testing Biometric Enrollment During Staff Creation")
    print("=" * 60)
    
    # Test data
    test_staff = {
        'staff_id': 'TEST001',
        'full_name': 'Test User',
        'password': 'test123',
        'email': '<EMAIL>',
        'phone': '******-0199',
        'department': 'Testing',
        'position': 'Test Engineer'
    }
    
    device_ip = '*************'
    
    print(f"📋 Test Staff Data:")
    print(f"   Staff ID: {test_staff['staff_id']}")
    print(f"   Name: {test_staff['full_name']}")
    print(f"   Device IP: {device_ip}")
    print()
    
    # Step 1: Test device connection
    print("🔌 Step 1: Testing device connection...")
    try:
        response = requests.post(f'{base_url}/test_biometric_connection', 
                               data={'device_ip': device_ip},
                               timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"   ✅ Device connected successfully")
                print(f"   📊 Device has {result.get('total_users', 0)} users")
            else:
                print(f"   ❌ Device connection failed: {result.get('message', 'Unknown error')}")
                return False
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("   ❌ Cannot connect to Flask application")
        print("   💡 Make sure the Flask app is running: python app.py")
        return False
    except Exception as e:
        print(f"   ❌ Error testing connection: {e}")
        return False
    
    # Step 2: Test user enrollment on device
    print("\n👤 Step 2: Testing user enrollment on device...")
    try:
        response = requests.post(f'{base_url}/enroll_biometric_user',
                               data={
                                   'device_ip': device_ip,
                                   'user_id': test_staff['staff_id'],
                                   'name': test_staff['full_name'],
                                   'privilege': 0
                               },
                               timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"   ✅ User enrolled on device successfully")
                print(f"   📝 Message: {result.get('message', '')}")
            else:
                print(f"   ⚠️  Enrollment response: {result.get('message', 'Unknown error')}")
                # This might fail if user already exists, which is okay for testing
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error enrolling user: {e}")
    
    # Step 3: Test enrollment mode
    print("\n🔧 Step 3: Testing enrollment mode...")
    try:
        response = requests.post(f'{base_url}/start_biometric_enrollment',
                               data={'device_ip': device_ip},
                               timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"   ✅ Enrollment mode started successfully")
                print(f"   📝 Message: {result.get('message', '')}")
                
                # Wait a moment then end enrollment mode
                print("   ⏳ Waiting 2 seconds...")
                time.sleep(2)
                
                # End enrollment mode
                end_response = requests.post(f'{base_url}/end_biometric_enrollment',
                                           data={'device_ip': device_ip},
                                           timeout=10)
                
                if end_response.status_code == 200:
                    end_result = end_response.json()
                    if end_result.get('success'):
                        print(f"   ✅ Enrollment mode ended successfully")
                    else:
                        print(f"   ⚠️  End enrollment: {end_result.get('message', '')}")
                        
            else:
                print(f"   ❌ Failed to start enrollment mode: {result.get('message', '')}")
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error with enrollment mode: {e}")
    
    # Step 4: Test enrollment verification
    print("\n✅ Step 4: Testing enrollment verification...")
    try:
        response = requests.post(f'{base_url}/verify_biometric_enrollment',
                               data={
                                   'device_ip': device_ip,
                                   'user_id': test_staff['staff_id']
                               },
                               timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                if result.get('enrolled'):
                    print(f"   ✅ User biometric data verified on device")
                else:
                    print(f"   ⚠️  User found but biometric data not captured")
                    print(f"   💡 This is expected if no actual biometric capture was performed")
                print(f"   📝 Message: {result.get('message', '')}")
            else:
                print(f"   ❌ Verification failed: {result.get('message', '')}")
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error verifying enrollment: {e}")
    
    # Step 5: Test staff creation without biometric enrollment
    print("\n🚫 Step 5: Testing staff creation without biometric enrollment...")
    try:
        response = requests.post(f'{base_url}/add_staff',
                               data={
                                   **test_staff,
                                   'biometric_enrolled': 'false'
                               },
                               timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if not result.get('success') and result.get('require_biometric'):
                print(f"   ✅ Correctly rejected staff creation without biometric enrollment")
                print(f"   📝 Error: {result.get('error', '')}")
            else:
                print(f"   ❌ Staff creation should have been rejected")
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error testing staff creation: {e}")
    
    # Step 6: Test staff creation with biometric enrollment
    print("\n✅ Step 6: Testing staff creation with biometric enrollment...")
    try:
        response = requests.post(f'{base_url}/add_staff',
                               data={
                                   **test_staff,
                                   'biometric_enrolled': 'true'
                               },
                               timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"   ✅ Staff created successfully with biometric enrollment")
            else:
                print(f"   ⚠️  Staff creation response: {result.get('error', '')}")
                # Might fail if staff already exists
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error creating staff: {e}")
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    print("✅ Device connection test completed")
    print("✅ User enrollment test completed")
    print("✅ Enrollment mode test completed")
    print("✅ Enrollment verification test completed")
    print("✅ Staff creation validation test completed")
    print("✅ Complete workflow test completed")
    print()
    print("🎯 NEXT STEPS:")
    print("1. Open the web interface: http://127.0.0.1:5000")
    print("2. Login as admin (school_admin / admin123)")
    print("3. Click 'Add Staff' to test the complete UI workflow")
    print("4. Follow the 2-step process: Staff Details → Biometric Enrollment")
    print()
    print("💡 NOTE: For actual biometric capture, the staff member must")
    print("   physically interact with the ZK device during enrollment.")
    
    return True

if __name__ == '__main__':
    test_biometric_enrollment()

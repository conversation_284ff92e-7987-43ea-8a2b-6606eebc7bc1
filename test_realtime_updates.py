#!/usr/bin/env python3
"""
Test script to simulate biometric verification and check real-time updates
"""

import sqlite3
import datetime
import time
import requests
import json

def create_test_staff():
    """Create a test staff member for testing"""
    db = sqlite3.connect('vishnorex.db')
    cursor = db.cursor()
    
    try:
        # Check if test staff exists
        cursor.execute("SELECT id FROM staff WHERE staff_id = 'TEST001'")
        existing = cursor.fetchone()
        
        if not existing:
            # Create test staff
            cursor.execute('''
                INSERT INTO staff (staff_id, full_name, department, position, school_id, password_hash)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', ('TEST001', 'Test Staff Member', 'IT Department', 'Developer', 1, 'test_hash'))
            
            staff_id = cursor.lastrowid
            print(f"✅ Created test staff with ID: {staff_id}")
        else:
            staff_id = existing[0]
            print(f"✅ Using existing test staff with ID: {staff_id}")
        
        db.commit()
        return staff_id
        
    except Exception as e:
        print(f"❌ Error creating test staff: {e}")
        return None
    finally:
        db.close()

def simulate_biometric_verification(staff_id, verification_type):
    """Simulate a biometric verification by directly updating the database"""
    db = sqlite3.connect('vishnorex.db')
    cursor = db.cursor()
    
    try:
        today = datetime.date.today()
        current_time = datetime.datetime.now().time().strftime('%H:%M:%S')
        
        print(f"🔄 Simulating {verification_type} for staff ID {staff_id} at {current_time}")
        
        # Check if attendance record exists
        cursor.execute("SELECT * FROM attendance WHERE staff_id = ? AND date = ?", (staff_id, today))
        existing = cursor.fetchone()
        
        if verification_type == 'check-in':
            if existing:
                cursor.execute('''
                    UPDATE attendance SET time_in = ?, status = 'present'
                    WHERE staff_id = ? AND date = ?
                ''', (current_time, staff_id, today))
            else:
                cursor.execute('''
                    INSERT INTO attendance (staff_id, school_id, date, time_in, status)
                    VALUES (?, ?, ?, ?, 'present')
                ''', (staff_id, 1, today, current_time))
                
        elif verification_type == 'check-out':
            cursor.execute('''
                UPDATE attendance SET time_out = ?
                WHERE staff_id = ? AND date = ?
            ''', (current_time, staff_id, today))
            
        elif verification_type == 'overtime-in':
            cursor.execute('''
                UPDATE attendance SET overtime_in = ?
                WHERE staff_id = ? AND date = ?
            ''', (current_time, staff_id, today))
            
        elif verification_type == 'overtime-out':
            cursor.execute('''
                UPDATE attendance SET overtime_out = ?
                WHERE staff_id = ? AND date = ?
            ''', (current_time, staff_id, today))
        
        # Log the verification
        cursor.execute('''
            INSERT INTO biometric_verifications 
            (staff_id, school_id, verification_type, verification_time, device_ip, biometric_method, verification_status)
            VALUES (?, ?, ?, ?, ?, ?, 'success')
        ''', (staff_id, 1, verification_type, datetime.datetime.now(), '*************', 'fingerprint'))
        
        db.commit()
        print(f"✅ {verification_type} recorded successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error simulating verification: {e}")
        return False
    finally:
        db.close()

def test_realtime_api():
    """Test the real-time API endpoint"""
    try:
        print("🧪 Testing real-time API endpoint...")
        response = requests.get('http://127.0.0.1:5000/get_realtime_attendance')
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Real-time API working")
            print(f"📊 Found {len(data.get('attendance_data', []))} attendance records")
            return True
        else:
            print(f"❌ API returned status code: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing API: {e}")
        return False

def run_simulation():
    """Run the complete simulation"""
    print("🚀 Starting Real-time Update Simulation")
    print("=" * 50)
    
    # Create test staff
    staff_id = create_test_staff()
    if not staff_id:
        return
    
    # Test API first
    if not test_realtime_api():
        print("❌ API test failed. Make sure the Flask app is running.")
        return
    
    print("\n🎬 Starting verification simulation...")
    print("👀 Watch the admin dashboard for real-time updates!")
    print("🌐 Open: http://127.0.0.1:5000 and login as admin")
    
    # Simulate different verification types with delays
    verifications = [
        ('check-in', 3),
        ('check-out', 5),
        ('overtime-in', 3),
        ('overtime-out', 3)
    ]
    
    for verification_type, delay in verifications:
        print(f"\n⏰ Waiting {delay} seconds before {verification_type}...")
        time.sleep(delay)
        
        success = simulate_biometric_verification(staff_id, verification_type)
        if success:
            print(f"✅ {verification_type} completed - check admin dashboard!")
        else:
            print(f"❌ {verification_type} failed")
    
    print("\n🎉 Simulation complete!")
    print("📱 The admin dashboard should now show all timing data in real-time")

if __name__ == "__main__":
    run_simulation()

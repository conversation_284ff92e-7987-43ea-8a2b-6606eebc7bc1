#!/usr/bin/env python3
"""
Comprehensive error checker for VishnoRex Staff Attendance System
Checks for common issues and validates the system
"""

import os
import re
import sqlite3
import requests
import time
from pathlib import Path

class ErrorChecker:
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.passed = []
        
    def log_error(self, message):
        self.errors.append(f"❌ {message}")
        
    def log_warning(self, message):
        self.warnings.append(f"⚠️  {message}")
        
    def log_pass(self, message):
        self.passed.append(f"✅ {message}")
    
    def check_file_exists(self, filepath, description=""):
        """Check if a file exists"""
        if os.path.exists(filepath):
            self.log_pass(f"File exists: {filepath} {description}")
            return True
        else:
            self.log_error(f"Missing file: {filepath} {description}")
            return False
    
    def check_python_syntax(self):
        """Check Python files for syntax errors"""
        python_files = ['app.py', 'database.py', 'zk_biometric.py', 'setup_demo_data.py']
        
        for file in python_files:
            if self.check_file_exists(file):
                try:
                    with open(file, 'r', encoding='utf-8') as f:
                        compile(f.read(), file, 'exec')
                    self.log_pass(f"Python syntax valid: {file}")
                except SyntaxError as e:
                    self.log_error(f"Syntax error in {file}: {e}")
                except Exception as e:
                    self.log_warning(f"Could not check {file}: {e}")
    
    def check_html_templates(self):
        """Check HTML templates for common issues"""
        template_dir = Path('templates')
        if not template_dir.exists():
            self.log_error("Templates directory missing")
            return
            
        html_files = list(template_dir.glob('*.html'))
        
        for html_file in html_files:
            try:
                with open(html_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check for DOCTYPE
                if '<!DOCTYPE html>' in content:
                    self.log_pass(f"DOCTYPE present: {html_file.name}")
                else:
                    self.log_warning(f"Missing DOCTYPE: {html_file.name}")
                
                # Check for lang attribute
                if 'lang=' in content:
                    self.log_pass(f"Lang attribute present: {html_file.name}")
                else:
                    self.log_warning(f"Missing lang attribute: {html_file.name}")
                
                # Check for inline styles (should be minimal now)
                inline_styles = re.findall(r'style\s*=\s*["\'][^"\']*["\']', content)
                if len(inline_styles) > 5:  # Allow some inline styles
                    self.log_warning(f"Many inline styles in {html_file.name}: {len(inline_styles)}")
                else:
                    self.log_pass(f"Minimal inline styles: {html_file.name}")
                
                # Check for CSRF tokens in forms
                forms = re.findall(r'<form[^>]*>', content)
                csrf_tokens = re.findall(r'csrf_token', content)
                if forms and csrf_tokens:
                    self.log_pass(f"CSRF tokens present: {html_file.name}")
                elif forms:
                    self.log_warning(f"Forms without CSRF tokens: {html_file.name}")
                
            except Exception as e:
                self.log_error(f"Error checking {html_file}: {e}")
    
    def check_css_files(self):
        """Check CSS files"""
        css_dir = Path('static/css')
        if css_dir.exists():
            css_files = list(css_dir.glob('*.css'))
            for css_file in css_files:
                self.log_pass(f"CSS file exists: {css_file.name}")
        else:
            self.log_warning("CSS directory missing")
    
    def check_javascript_files(self):
        """Check JavaScript files for basic syntax"""
        js_dir = Path('static/js')
        if not js_dir.exists():
            self.log_error("JavaScript directory missing")
            return
            
        js_files = list(js_dir.glob('*.js'))
        
        for js_file in js_files:
            try:
                with open(js_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check for CSRF token usage
                if 'getCSRFToken' in content:
                    self.log_pass(f"CSRF token function present: {js_file.name}")
                else:
                    self.log_warning(f"No CSRF token function: {js_file.name}")
                
                # Check for fetch requests with CSRF
                fetch_posts = re.findall(r'fetch\([^)]*method.*POST', content, re.IGNORECASE)
                csrf_in_fetch = re.findall(r'csrf_token.*encodeURIComponent', content)
                
                if fetch_posts and csrf_in_fetch:
                    self.log_pass(f"POST requests have CSRF protection: {js_file.name}")
                elif fetch_posts:
                    self.log_warning(f"POST requests may lack CSRF: {js_file.name}")
                
            except Exception as e:
                self.log_error(f"Error checking {js_file}: {e}")
    
    def check_database(self):
        """Check database connectivity and structure"""
        try:
            conn = sqlite3.connect('vishnorex.db')
            cursor = conn.cursor()
            
            # Check if main tables exist
            tables = ['schools', 'admins', 'company_admins', 'staff', 'attendance', 'leave_applications']
            
            for table in tables:
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table,))
                if cursor.fetchone():
                    self.log_pass(f"Database table exists: {table}")
                else:
                    self.log_error(f"Missing database table: {table}")
            
            conn.close()
            
        except Exception as e:
            self.log_error(f"Database check failed: {e}")
    
    def check_flask_app(self):
        """Check if Flask app is running and responding"""
        try:
            response = requests.get('http://127.0.0.1:5000', timeout=5)
            if response.status_code == 200:
                self.log_pass("Flask application is running and responding")
                
                # Check if login page loads
                if 'Staff Attendance System' in response.text:
                    self.log_pass("Main page content loads correctly")
                else:
                    self.log_warning("Main page content may be incorrect")
                    
            else:
                self.log_error(f"Flask app returned status {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            self.log_warning("Flask application not running (start with: python app.py)")
        except Exception as e:
            self.log_error(f"Error checking Flask app: {e}")
    
    def check_dependencies(self):
        """Check if required Python packages are installed"""
        required_packages = [
            'flask', 'flask_wtf', 'werkzeug', 'pyzk', 'pymysql'
        ]
        
        for package in required_packages:
            try:
                __import__(package)
                self.log_pass(f"Package installed: {package}")
            except ImportError:
                self.log_error(f"Missing package: {package} (install with: pip install {package})")
    
    def run_all_checks(self):
        """Run all error checks"""
        print("🔍 VishnoRex Error Checker")
        print("=" * 50)
        
        print("\n📁 Checking file structure...")
        essential_files = [
            ('app.py', '(main application)'),
            ('database.py', '(database module)'),
            ('requirements.txt', '(dependencies)'),
            ('templates/index.html', '(main template)'),
            ('static/css/styles.css', '(main stylesheet)'),
            ('static/js/main.js', '(main JavaScript)')
        ]
        
        for file, desc in essential_files:
            self.check_file_exists(file, desc)
        
        print("\n🐍 Checking Python syntax...")
        self.check_python_syntax()
        
        print("\n📦 Checking dependencies...")
        self.check_dependencies()
        
        print("\n🗄️  Checking database...")
        self.check_database()
        
        print("\n🌐 Checking HTML templates...")
        self.check_html_templates()
        
        print("\n🎨 Checking CSS files...")
        self.check_css_files()
        
        print("\n⚡ Checking JavaScript files...")
        self.check_javascript_files()
        
        print("\n🚀 Checking Flask application...")
        self.check_flask_app()
        
        # Print summary
        print("\n" + "=" * 50)
        print("📊 SUMMARY")
        print("=" * 50)
        
        print(f"\n✅ PASSED ({len(self.passed)}):")
        for item in self.passed:
            print(f"  {item}")
        
        if self.warnings:
            print(f"\n⚠️  WARNINGS ({len(self.warnings)}):")
            for item in self.warnings:
                print(f"  {item}")
        
        if self.errors:
            print(f"\n❌ ERRORS ({len(self.errors)}):")
            for item in self.errors:
                print(f"  {item}")
        else:
            print(f"\n🎉 NO CRITICAL ERRORS FOUND!")
        
        print(f"\nTotal: {len(self.passed)} passed, {len(self.warnings)} warnings, {len(self.errors)} errors")
        
        return len(self.errors) == 0

if __name__ == '__main__':
    checker = ErrorChecker()
    success = checker.run_all_checks()
    
    if success:
        print("\n🎯 System appears to be working correctly!")
    else:
        print("\n🔧 Please fix the errors above before proceeding.")

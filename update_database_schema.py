#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to update the database schema for the enhanced biometric attendance system
"""

import sqlite3
import os

def update_database_schema():
    """Update the database schema to support the new biometric attendance features"""
    print("🔧 Updating Database Schema for Enhanced Biometric Attendance")
    print("=" * 60)
    
    db_path = 'attendance.db'
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print(f"📊 Connected to database: {db_path}")
        
        # Step 1: Check current attendance table schema
        print("\n📋 Step 1: Checking current attendance table schema...")
        cursor.execute("PRAGMA table_info(attendance)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        print(f"   Current columns: {column_names}")
        
        # Step 2: Add overtime columns if they don't exist
        print("\n🔄 Step 2: Adding overtime columns...")
        
        if 'overtime_in' not in column_names:
            cursor.execute("ALTER TABLE attendance ADD COLUMN overtime_in TIME")
            print("   ✅ Added overtime_in column")
        else:
            print("   ✅ overtime_in column already exists")
        
        if 'overtime_out' not in column_names:
            cursor.execute("ALTER TABLE attendance ADD COLUMN overtime_out TIME")
            print("   ✅ Added overtime_out column")
        else:
            print("   ✅ overtime_out column already exists")
        
        # Step 3: Create biometric_verifications table
        print("\n📝 Step 3: Creating biometric_verifications table...")
        
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS biometric_verifications (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            staff_id INTEGER NOT NULL,
            school_id INTEGER NOT NULL,
            verification_type TEXT CHECK(verification_type IN ('check-in', 'check-out', 'overtime-in', 'overtime-out')) NOT NULL,
            verification_time DATETIME NOT NULL,
            device_ip TEXT,
            biometric_method TEXT CHECK(biometric_method IN ('fingerprint', 'face', 'card', 'password')),
            verification_status TEXT CHECK(verification_status IN ('success', 'failed', 'retry')) DEFAULT 'success',
            notes TEXT,
            FOREIGN KEY (staff_id) REFERENCES staff(id),
            FOREIGN KEY (school_id) REFERENCES schools(id)
        )
        ''')
        print("   ✅ Created biometric_verifications table")
        
        # Step 4: Verify the changes
        print("\n✅ Step 4: Verifying schema changes...")
        
        # Check attendance table
        cursor.execute("PRAGMA table_info(attendance)")
        updated_columns = cursor.fetchall()
        updated_column_names = [col[1] for col in updated_columns]
        
        required_columns = ['overtime_in', 'overtime_out']
        missing_columns = [col for col in required_columns if col not in updated_column_names]
        
        if missing_columns:
            print(f"   ❌ Still missing columns: {missing_columns}")
        else:
            print("   ✅ All required columns present in attendance table")
        
        # Check biometric_verifications table
        cursor.execute("PRAGMA table_info(biometric_verifications)")
        verification_columns = cursor.fetchall()
        verification_column_names = [col[1] for col in verification_columns]
        
        expected_verification_columns = [
            'id', 'staff_id', 'school_id', 'verification_type', 
            'verification_time', 'device_ip', 'biometric_method', 
            'verification_status', 'notes'
        ]
        
        missing_verification_columns = [
            col for col in expected_verification_columns 
            if col not in verification_column_names
        ]
        
        if missing_verification_columns:
            print(f"   ❌ Missing verification columns: {missing_verification_columns}")
        else:
            print("   ✅ All required columns present in biometric_verifications table")
        
        # Commit changes
        conn.commit()
        print("\n💾 Changes committed to database")
        
        # Step 5: Show final schema
        print("\n📊 Final Schema:")
        print("\nAttendance table columns:")
        for col in updated_columns:
            print(f"   - {col[1]} ({col[2]})")
        
        print("\nBiometric verifications table columns:")
        for col in verification_columns:
            print(f"   - {col[1]} ({col[2]})")
        
        conn.close()
        
        print("\n🎉 Database schema update completed successfully!")
        print("\nYou can now:")
        print("1. Start the Flask application: python app.py")
        print("2. Test the biometric attendance system")
        print("3. Use the four verification types: check-in, check-out, overtime-in, overtime-out")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Error updating database schema: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    try:
        update_database_schema()
    except KeyboardInterrupt:
        print("\n\n⚠️  Update interrupted by user")
    except Exception as e:
        print(f"\n\n❌ Update failed with error: {e}")
        import traceback
        traceback.print_exc()

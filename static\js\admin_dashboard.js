document.addEventListener('DOMContentLoaded', function () {
    // Helper function to get CSRF token
    function getCSRFToken() {
        const token = document.querySelector('input[name="csrf_token"]');
        return token ? token.value : '';
    }

    // Helper function to enroll user on device
    function enrollUserOnDevice(deviceIP, staffId, fullName, overwrite = false) {
        return fetch('/enroll_biometric_user', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: `device_ip=${encodeURIComponent(deviceIP)}&user_id=${encodeURIComponent(staffId)}&name=${encodeURIComponent(fullName)}&overwrite=${overwrite}&csrf_token=${encodeURIComponent(getCSRFToken())}`
        })
        .then(response => response.json());
    }
    // Initialize Chart
    const ctx = document.getElementById('attendanceChart')?.getContext('2d');
    if (ctx) {
        const attendanceChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Present', 'Absent', 'Late'],
                datasets: [{
                    data: [85, 10, 5], // Placeholder
                    backgroundColor: ['#198754', '#dc3545', '#ffc107'],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: { legend: { position: 'bottom' } }
            }
        });
    }

    // Staff Calendar Init
    const staffCalendarEl = document.getElementById('staffCalendar');
    let staffCalendar;
    if (staffCalendarEl) {
        staffCalendar = new FullCalendar.Calendar(staffCalendarEl, {
            initialView: 'dayGridMonth',
            headerToolbar: {
                left: 'prev,next today',
                center: 'title',
                right: 'dayGridMonth,timeGridWeek,timeGridDay'
            },
            events: [],
            eventClick: function (info) {
                alert('Event: ' + info.event.title);
            }
        });
        staffCalendar.render();
    }

    // Live Staff Search
    document.getElementById('staffSearch')?.addEventListener('input', function () {
        const searchTerm = this.value.toLowerCase();
        const rows = document.querySelectorAll('.table tbody tr');
        rows.forEach(row => {
            const match = Array.from(row.querySelectorAll('td')).some(cell => cell.textContent.toLowerCase().includes(searchTerm));
            if (match) {
                row.classList.remove('hidden');
            } else {
                row.classList.add('hidden');
            }
        });
    });

    // View Staff Profile Handler
    document.querySelector('.table tbody')?.addEventListener('click', function (e) {
        const target = e.target.closest('.view-profile, .staff-profile');
        if (!target) return;
        e.preventDefault();
        const staffId = target.getAttribute('data-staff-id');
        loadStaffProfile(staffId);
    });

    function loadStaffProfile(staffId) {
        fetch(`/get_staff_details?id=${staffId}`)
            .then(res => res.json())
            .then(data => {
                if (!data.success) return;

                // Fill Staff Info
                const staff = data.staff;
                document.getElementById('staffProfileCard').classList.remove('hidden');
                document.getElementById('staffName').textContent = staff.full_name;
                document.getElementById('staffId').textContent = staff.staff_id;
                document.getElementById('staffDept').textContent = staff.department;
                document.getElementById('staffPosition').textContent = staff.position;
                document.getElementById('staffEmail').textContent = staff.email;
                document.getElementById('staffPhone').textContent = staff.phone;
                document.getElementById('staffPhoto').src = staff.photo_url || 'https://via.placeholder.com/150';

                // Attendance Calendar
                if (staffCalendar) {
                    staffCalendar.removeAllEvents();
                    staffCalendar.addEventSource(data.attendance.map(record => {
                        let color = {
                            present: '#198754',
                            absent: '#dc3545',
                            late: '#ffc107',
                            leave: '#0dcaf0'
                        }[record.status];
                        return {
                            title: `${record.status}${record.time_in ? ' (' + record.time_in + ')' : ''}`,
                            start: record.date,
                            allDay: true,
                            backgroundColor: color,
                            borderColor: color
                        };
                    }));
                }

                // Attendance Counts
                const counts = { present: 0, absent: 0, late: 0, leave: 0 };
                data.attendance.forEach(r => counts[r.status]++);
                document.getElementById('presentCount').textContent = counts.present;
                document.getElementById('absentCount').textContent = counts.absent;
                document.getElementById('lateCount').textContent = counts.late;
                document.getElementById('leaveCount').textContent = counts.leave;

                // Recent Activity
                const activityList = document.getElementById('recentActivity');
                activityList.innerHTML = '';
                data.recent_activity?.forEach(activity => {
                    const item = document.createElement('div');
                    item.className = 'list-group-item';
                    item.innerHTML = `<div class="d-flex justify-content-between">
                        <small>${activity.description}</small>
                        <small class="text-muted">${activity.timestamp}</small>
                    </div>`;
                    activityList.appendChild(item);
                });

                // Edit Staff
                document.getElementById('editStaffBtn').onclick = function () {
                    document.getElementById('editStaffId').value = staffId;
                    document.getElementById('editFullName').value = staff.full_name;
                    document.getElementById('editEmail').value = staff.email || '';
                    document.getElementById('editPhone').value = staff.phone || '';
                    document.getElementById('editDepartment').value = staff.department || '';
                    document.getElementById('editPosition').value = staff.position || '';
                    document.getElementById('editStatus').checked = staff.status === 'active';
                    new bootstrap.Modal(document.getElementById('editStaffModal')).show();
                };

                // Reset Password
                document.getElementById('resetPasswordBtn').onclick = function () {
                    if (confirm(`Reset password for ${staff.full_name}?`)) {
                        fetch('/reset_staff_password', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                            body: `staff_id=${staffId}&csrf_token=${encodeURIComponent(getCSRFToken())}`
                        })
                        .then(res => res.json())
                        .then(result => {
                            if (result.success) alert(`Temporary password: ${result.temp_password}`);
                            else alert(result.error || 'Password reset failed');
                        });
                    }
                };

                // Delete Staff
                // Update the delete staff function in admin_dashboard.js
document.getElementById('deleteStaffBtn')?.addEventListener('click', function() {
    const staffId = document.getElementById('editStaffId').value;
    const staffName = document.getElementById('staffName').textContent;
    
    if (confirm(`Are you sure you want to delete ${staffName}? This action cannot be undone.`)) {
        fetch('/delete_staff', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `staff_id=${staffId}&csrf_token=${encodeURIComponent(getCSRFToken())}`
        })
        .then(res => res.json())
        .then(data => {
            if (data.success) {
                alert('Staff deleted successfully');
                window.location.href = "{{ url_for('admin_dashboard') }}";
            } else {
                alert(data.error || 'Failed to delete staff');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while deleting staff');
        });
    }
});
            });
    }

    // Save Edited Staff
    document.getElementById('saveEditStaff')?.addEventListener('click', function () {
        const formData = new FormData();
        formData.append('staff_id', document.getElementById('editStaffId').value);
        formData.append('full_name', document.getElementById('editFullName').value);
        formData.append('email', document.getElementById('editEmail').value);
        formData.append('phone', document.getElementById('editPhone').value);
        formData.append('department', document.getElementById('editDepartment').value);
        formData.append('position', document.getElementById('editPosition').value);
        formData.append('status', document.getElementById('editStatus').checked ? 'active' : 'inactive');

        const photoFile = document.getElementById('editPhoto').files[0];
        if (photoFile) formData.append('photo', photoFile);
        formData.append('csrf_token', getCSRFToken());

        fetch('/update_staff', {
            method: 'POST',
            body: formData
        })
        .then(res => res.json())
        .then(data => {
            if (data.success) {
                alert('Staff updated');
                location.reload();
            } else alert(data.error || 'Update failed');
        });
    });

    // Staff Creation - Step Navigation
    document.getElementById('nextStepBtn')?.addEventListener('click', function () {
        const staffId = document.getElementById('staffId').value;
        const fullName = document.getElementById('fullName').value;
        const password = document.getElementById('password').value;

        if (!staffId || !fullName || !password) {
            alert('Staff ID, Name, and Password are required');
            return;
        }

        // Move to biometric enrollment step
        document.getElementById('staffDetailsStep').classList.add('hidden');
        document.getElementById('biometricEnrollmentStep').classList.remove('hidden');
        document.getElementById('nextStepBtn').classList.add('hidden');
        document.getElementById('saveStaff').classList.remove('hidden');

        // Update progress bar
        document.getElementById('staffCreationProgress').style.width = '100%';
    });

    // Start Biometric Enrollment
    document.getElementById('startEnrollmentBtn')?.addEventListener('click', function () {
        const staffId = document.getElementById('staffId').value;
        const fullName = document.getElementById('fullName').value;
        const deviceIP = '*************'; // Default device IP

        if (!staffId || !fullName) {
            alert('Please complete staff details first');
            return;
        }

        const startBtn = document.getElementById('startEnrollmentBtn');
        const statusDiv = document.getElementById('enrollmentDeviceStatus');
        const progressDiv = document.getElementById('staffEnrollmentProgress');
        const progressBar = document.getElementById('staffEnrollmentProgressBar');
        const statusText = document.getElementById('staffEnrollmentStatus');

        startBtn.disabled = true;
        startBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Starting...';

        statusDiv.className = 'alert alert-info';
        statusDiv.innerHTML = '<i class="bi bi-hourglass-split"></i> Preparing device for enrollment...';

        progressDiv.classList.remove('hidden');
        progressBar.style.width = '25%';
        statusText.textContent = 'Checking existing users...';

        // Step 1: Check if user already exists
        fetch('/check_biometric_user', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: `device_ip=${encodeURIComponent(deviceIP)}&user_id=${encodeURIComponent(staffId)}&csrf_token=${encodeURIComponent(getCSRFToken())}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.user_exists) {
                // User already exists, ask for confirmation
                const existingUser = data.user_data;
                const confirmOverwrite = confirm(
                    `User ${staffId} already exists on the device:\n` +
                    `Name: ${existingUser.name}\n` +
                    `Privilege: ${existingUser.privilege}\n\n` +
                    `Do you want to overwrite the existing user?`
                );

                if (!confirmOverwrite) {
                    // User chose not to overwrite
                    startBtn.disabled = false;
                    startBtn.innerHTML = '<i class="bi bi-fingerprint"></i> Start Enrollment';
                    statusDiv.className = 'alert alert-warning';
                    statusDiv.innerHTML = `
                        <i class="bi bi-exclamation-triangle"></i>
                        <strong>User Already Exists</strong><br>
                        User ${staffId} (${existingUser.name}) is already enrolled on the device.
                        Click "Start Enrollment" again to overwrite.
                    `;
                    progressDiv.classList.add('hidden');
                    return Promise.reject(new Error('User chose not to overwrite'));
                }

                // User chose to overwrite, proceed with overwrite=true
                statusText.textContent = 'Overwriting existing user...';
                return enrollUserOnDevice(deviceIP, staffId, fullName, true);
            } else {
                // User doesn't exist, proceed with normal enrollment
                statusText.textContent = 'Enrolling new user...';
                return enrollUserOnDevice(deviceIP, staffId, fullName, false);
            }
        })
        .then(enrollResult => {
            if (enrollResult && enrollResult.success) {
                progressBar.style.width = '50%';
                statusText.textContent = 'User enrolled. Starting enrollment mode...';

                // Step 2: Start enrollment mode
                return fetch('/start_biometric_enrollment', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: `device_ip=${encodeURIComponent(deviceIP)}&csrf_token=${encodeURIComponent(getCSRFToken())}`
                });
            } else if (enrollResult) {
                throw new Error(enrollResult.message || 'Failed to enroll user');
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                progressBar.style.width = '75%';
                statusText.textContent = 'Device ready. Please capture biometric data...';

                statusDiv.className = 'alert alert-warning';
                statusDiv.innerHTML = `
                    <i class="bi bi-fingerprint"></i>
                    <strong>Device Ready!</strong><br>
                    Please ask ${fullName} to place their finger on the biometric device and follow the device prompts.
                `;

                startBtn.classList.add('hidden');
                document.getElementById('triggerEnrollmentBtn').classList.remove('hidden');
            } else {
                throw new Error(data.message || 'Failed to start enrollment mode');
            }
        })
        .catch(error => {
            startBtn.disabled = false;
            startBtn.innerHTML = '<i class="bi bi-fingerprint"></i> Start Enrollment';
            statusDiv.className = 'alert alert-danger';
            statusDiv.innerHTML = `<i class="bi bi-x-circle"></i> Error: ${error.message}`;
            progressDiv.classList.add('hidden');
        });
    });

    // Trigger Biometric Enrollment
    document.getElementById('triggerEnrollmentBtn')?.addEventListener('click', function () {
        const staffId = document.getElementById('staffId').value;
        const fullName = document.getElementById('fullName').value;
        const deviceIP = '*************';

        const triggerBtn = document.getElementById('triggerEnrollmentBtn');
        const verifyBtn = document.getElementById('verifyEnrollmentBtn');
        const statusDiv = document.getElementById('enrollmentDeviceStatus');
        const progressBar = document.getElementById('staffEnrollmentProgressBar');
        const statusText = document.getElementById('staffEnrollmentStatus');

        triggerBtn.disabled = true;
        triggerBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Starting...';

        statusText.textContent = 'Starting biometric enrollment...';
        progressBar.style.width = '50%';

        fetch('/verify_biometric_enrollment', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: `device_ip=${encodeURIComponent(deviceIP)}&user_id=${encodeURIComponent(staffId)}&name=${encodeURIComponent(fullName)}&trigger_enrollment=true&csrf_token=${encodeURIComponent(getCSRFToken())}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.enrollment_started) {
                progressBar.style.width = '75%';
                statusText.textContent = 'Biometric enrollment started. Please scan fingerprint...';

                statusDiv.className = 'alert alert-info';
                statusDiv.innerHTML = `
                    <i class="bi bi-fingerprint"></i>
                    <strong>Enrollment Started!</strong><br>
                    ${data.message}<br>
                    ${data.manual_mode ? 'Please use the device interface to complete enrollment.' : 'Please follow the device prompts to scan your biometric data.'}
                `;

                triggerBtn.classList.add('hidden');
                verifyBtn.classList.remove('hidden');
                verifyBtn.disabled = false;
            } else if (data.success && data.enrolled) {
                // User already enrolled
                progressBar.style.width = '100%';
                statusText.textContent = 'Biometric enrollment verified!';

                statusDiv.className = 'alert alert-success';
                statusDiv.innerHTML = `
                    <i class="bi bi-check-circle"></i>
                    <strong>Already Enrolled!</strong><br>
                    Biometric data already exists for ${fullName}.
                `;

                document.getElementById('biometricEnrolled').value = 'true';
                triggerBtn.classList.add('hidden');
                verifyBtn.classList.add('hidden');
                document.getElementById('saveStaff').disabled = false;
                document.getElementById('saveStaff').innerHTML = '<i class="bi bi-check-circle"></i> Create Staff Account';
            } else {
                throw new Error(data.message || 'Failed to start enrollment');
            }
        })
        .catch(error => {
            triggerBtn.disabled = false;
            triggerBtn.innerHTML = '<i class="bi bi-fingerprint"></i> Start Biometric Scan';
            statusDiv.className = 'alert alert-danger';
            statusDiv.innerHTML = `<i class="bi bi-x-circle"></i> Failed to start enrollment: ${error.message}`;
        });
    });

    // Verify Biometric Enrollment
    document.getElementById('verifyEnrollmentBtn')?.addEventListener('click', function () {
        const staffId = document.getElementById('staffId').value;
        const deviceIP = '*************';

        const verifyBtn = document.getElementById('verifyEnrollmentBtn');
        const statusDiv = document.getElementById('enrollmentDeviceStatus');
        const progressBar = document.getElementById('staffEnrollmentProgressBar');
        const statusText = document.getElementById('staffEnrollmentStatus');

        verifyBtn.disabled = true;
        verifyBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Verifying...';

        statusText.textContent = 'Verifying biometric enrollment...';

        fetch('/verify_biometric_enrollment', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: `device_ip=${encodeURIComponent(deviceIP)}&user_id=${encodeURIComponent(staffId)}&csrf_token=${encodeURIComponent(getCSRFToken())}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.enrolled) {
                progressBar.style.width = '100%';
                statusText.textContent = 'Biometric enrollment verified!';

                statusDiv.className = 'alert alert-success';
                statusDiv.innerHTML = `
                    <i class="bi bi-check-circle"></i>
                    <strong>Enrollment Complete!</strong><br>
                    Biometric data has been successfully captured for ${document.getElementById('fullName').value}.
                `;

                // Mark as enrolled
                document.getElementById('biometricEnrolled').value = 'true';

                verifyBtn.classList.add('hidden');

                // Enable save button
                document.getElementById('saveStaff').disabled = false;
                document.getElementById('saveStaff').innerHTML = '<i class="bi bi-check-circle"></i> Create Staff Account';

            } else {
                statusDiv.className = 'alert alert-warning';
                statusDiv.innerHTML = `
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>Enrollment Not Complete</strong><br>
                    Please ensure the staff member has completed biometric capture on the device.<br>
                    <small>Try scanning again or use the device interface directly.</small>
                `;

                verifyBtn.disabled = false;
                verifyBtn.innerHTML = '<i class="bi bi-check-circle"></i> Verify Enrollment';
            }
        })
        .catch(error => {
            verifyBtn.disabled = false;
            verifyBtn.innerHTML = '<i class="bi bi-check-circle"></i> Verify Enrollment';
            statusDiv.className = 'alert alert-danger';
            statusDiv.innerHTML = `<i class="bi bi-x-circle"></i> Verification failed: ${error.message}`;
        });
    });

    // Add Staff (Final Step)
    document.getElementById('saveStaff')?.addEventListener('click', function () {
        const staffId = document.getElementById('staffId').value;
        const fullName = document.getElementById('fullName').value;
        const password = document.getElementById('password').value;
        const email = document.getElementById('email').value;
        const phone = document.getElementById('phone').value;
        const department = document.getElementById('department').value;
        const position = document.getElementById('position').value;
        const biometricEnrolled = document.getElementById('biometricEnrolled').value;

        if (!staffId || !fullName || !password) {
            alert('Staff ID, Name, and Password are required');
            return;
        }

        if (biometricEnrolled !== 'true') {
            alert('Biometric enrollment must be completed before creating staff account');
            return;
        }

        const saveBtn = document.getElementById('saveStaff');
        saveBtn.disabled = true;
        saveBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Creating Account...';

        fetch('/add_staff', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: `staff_id=${staffId}&full_name=${encodeURIComponent(fullName)}&password=${encodeURIComponent(password)}&email=${email}&phone=${phone}&department=${encodeURIComponent(department)}&position=${encodeURIComponent(position)}&biometric_enrolled=${biometricEnrolled}&csrf_token=${encodeURIComponent(getCSRFToken())}`
        })
        .then(res => res.json())
        .then(data => {
            if (data.success) {
                alert('Staff account created successfully with biometric enrollment!');
                bootstrap.Modal.getInstance(document.getElementById('addStaffModal')).hide();
                location.reload();
            } else {
                if (data.require_biometric) {
                    alert('Biometric enrollment is required. Please complete the enrollment process.');
                } else {
                    alert(data.error || 'Failed to create staff account');
                }
                saveBtn.disabled = false;
                saveBtn.innerHTML = '<i class="bi bi-check-circle"></i> Create Staff Account';
            }
        })
        .catch(error => {
            alert('Error creating staff account: ' + error.message);
            saveBtn.disabled = false;
            saveBtn.innerHTML = '<i class="bi bi-check-circle"></i> Create Staff Account';
        });
    });

    // Export Staff
    document.getElementById('exportStaffBtn')?.addEventListener('click', function () {
        const start = prompt('Start date (YYYY-MM-DD):');
        const end = prompt('End date (YYYY-MM-DD):');
        if (!start || !end) return;

        fetch(`/export_staff_data?start_date=${start}&end_date=${end}`)
            .then(res => res.blob())
            .then(blob => {
                const a = document.createElement('a');
                a.href = URL.createObjectURL(blob);
                a.download = `staff_data_${start}_to_${end}.csv`;
                a.click();
                URL.revokeObjectURL(a.href);
            });
    });

    // Biometric Enrollment (Fake Simulation)
    const biometricModal = document.getElementById('biometricModal');
    if (biometricModal) {
        biometricModal.addEventListener('show.bs.modal', function () {
            let count = 0;
            const progressBar = document.getElementById('progressBar');
            const status = document.getElementById('enrollmentStatus');
            const done = document.getElementById('enrollmentComplete');
            const progressWrap = document.getElementById('enrollmentProgress');
            done.classList.add('hidden');
            progressWrap.classList.remove('hidden');
            const interval = setInterval(() => {
                count++;
                progressBar.style.width = `${(count / 5) * 100}%`;
                status.innerHTML = `<div class="alert alert-info">Scan ${count} of 5 completed</div>`;
                if (count >= 5) {
                    clearInterval(interval);
                    progressWrap.classList.add('hidden');
                    done.classList.remove('hidden');
                }
            }, 2000);
        });
    }

    // Leave Processing
    document.querySelectorAll('.approve-btn, .reject-btn').forEach(btn => {
        btn.addEventListener('click', function () {
            const decision = this.classList.contains('approve-btn') ? 'approve' : 'reject';
            const leaveId = this.getAttribute('data-leave-id');
            if (!confirm(`Are you sure to ${decision} this leave?`)) return;

            fetch('/process_leave', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `leave_id=${leaveId}&decision=${decision}&csrf_token=${encodeURIComponent(getCSRFToken())}`
            })
            .then(res => res.json())
            .then(data => {
                if (data.success) {
                    alert(`Leave ${decision}d`);
                    location.reload();
                } else alert(data.error || `Failed to ${decision} leave`);
            });
        });
    });

    // Reset Add Staff Modal when closed
    document.getElementById('addStaffModal')?.addEventListener('hidden.bs.modal', function () {
        // Reset form
        document.getElementById('staffForm').reset();
        document.getElementById('biometricEnrolled').value = 'false';

        // Reset steps
        document.getElementById('staffDetailsStep').classList.remove('hidden');
        document.getElementById('biometricEnrollmentStep').classList.add('hidden');

        // Reset buttons
        document.getElementById('nextStepBtn').classList.remove('hidden');
        document.getElementById('saveStaff').classList.add('hidden');
        document.getElementById('startEnrollmentBtn').classList.remove('hidden');
        document.getElementById('triggerEnrollmentBtn').classList.add('hidden');
        document.getElementById('verifyEnrollmentBtn').classList.add('hidden');

        // Reset progress
        document.getElementById('staffCreationProgress').style.width = '50%';
        document.getElementById('staffEnrollmentProgress').classList.add('hidden');

        // Reset status
        document.getElementById('enrollmentDeviceStatus').className = 'alert alert-secondary';
        document.getElementById('enrollmentDeviceStatus').innerHTML = '<i class="bi bi-info-circle"></i> Click "Start Biometric Scan" to begin';

        // Reset button states
        document.getElementById('startEnrollmentBtn').disabled = false;
        document.getElementById('startEnrollmentBtn').innerHTML = '<i class="bi bi-fingerprint"></i> Start Enrollment';
        document.getElementById('saveStaff').disabled = false;
        document.getElementById('saveStaff').innerHTML = '<i class="bi bi-check-circle"></i> Create Staff Account';
    });

    // Biometric Device Management
    const testConnectionBtn = document.getElementById('testConnectionBtn');
    const syncAttendanceBtn = document.getElementById('syncAttendanceBtn');
    const loadUsersBtn = document.getElementById('loadUsersBtn');
    const deviceStatus = document.getElementById('deviceStatus');
    const syncResults = document.getElementById('syncResults');
    const deviceUsers = document.getElementById('deviceUsers');

    testConnectionBtn?.addEventListener('click', function() {
        const deviceIP = document.getElementById('deviceIP').value;
        testConnectionBtn.disabled = true;
        testConnectionBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Testing...';

        deviceStatus.className = 'alert alert-info';
        deviceStatus.innerHTML = '<i class="bi bi-hourglass-split"></i> Testing connection...';

        fetch('/test_biometric_connection', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `device_ip=${encodeURIComponent(deviceIP)}&csrf_token=${encodeURIComponent(getCSRFToken())}`
        })
        .then(response => response.json())
        .then(data => {
            testConnectionBtn.disabled = false;
            testConnectionBtn.innerHTML = '<i class="bi bi-wifi"></i> Test Connection';

            if (data.success) {
                deviceStatus.className = 'alert alert-success';
                deviceStatus.innerHTML = `
                    <i class="bi bi-check-circle"></i>
                    Connection successful! Device has ${data.total_users} users.
                `;
            } else {
                deviceStatus.className = 'alert alert-danger';
                deviceStatus.innerHTML = `
                    <i class="bi bi-x-circle"></i>
                    Connection failed: ${data.message}
                `;
            }
        })
        .catch(error => {
            testConnectionBtn.disabled = false;
            testConnectionBtn.innerHTML = '<i class="bi bi-wifi"></i> Test Connection';
            deviceStatus.className = 'alert alert-danger';
            deviceStatus.innerHTML = `<i class="bi bi-x-circle"></i> Error: ${error.message}`;
        });
    });

    syncAttendanceBtn?.addEventListener('click', function() {
        const deviceIP = document.getElementById('deviceIP').value;
        syncAttendanceBtn.disabled = true;
        syncAttendanceBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Syncing...';

        fetch('/sync_biometric_attendance', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `device_ip=${encodeURIComponent(deviceIP)}&csrf_token=${encodeURIComponent(getCSRFToken())}`
        })
        .then(response => response.json())
        .then(data => {
            syncAttendanceBtn.disabled = false;
            syncAttendanceBtn.innerHTML = '<i class="bi bi-arrow-clockwise"></i> Sync Attendance';

            // Update sync results
            document.getElementById('totalRecords').textContent = data.total_records || 0;
            document.getElementById('sqliteSynced').textContent = data.sqlite_synced || 0;
            document.getElementById('mysqlSynced').textContent = data.mysql_synced || 0;
            syncResults.classList.remove('hidden');

            if (data.success) {
                deviceStatus.className = 'alert alert-success';
                deviceStatus.innerHTML = `<i class="bi bi-check-circle"></i> ${data.message}`;
            } else {
                deviceStatus.className = 'alert alert-warning';
                deviceStatus.innerHTML = `<i class="bi bi-exclamation-triangle"></i> ${data.message}`;
            }
        })
        .catch(error => {
            syncAttendanceBtn.disabled = false;
            syncAttendanceBtn.innerHTML = '<i class="bi bi-arrow-clockwise"></i> Sync Attendance';
            deviceStatus.className = 'alert alert-danger';
            deviceStatus.innerHTML = `<i class="bi bi-x-circle"></i> Sync error: ${error.message}`;
        });
    });

    loadUsersBtn?.addEventListener('click', function() {
        const deviceIP = document.getElementById('deviceIP').value;
        loadUsersBtn.disabled = true;
        loadUsersBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Loading...';

        fetch(`/get_biometric_users?device_ip=${encodeURIComponent(deviceIP)}`)
        .then(response => response.json())
        .then(data => {
            loadUsersBtn.disabled = false;
            loadUsersBtn.innerHTML = '<i class="bi bi-people"></i> Load Users from Device';

            if (data.success) {
                const tbody = document.getElementById('usersTableBody');
                const userCount = document.getElementById('userCount');
                tbody.innerHTML = '';

                data.users.forEach(user => {
                    const row = tbody.insertRow();
                    row.innerHTML = `
                        <td>${user.user_id}</td>
                        <td>${user.name || 'N/A'}</td>
                        <td>${user.privilege}</td>
                        <td>${user.group_id}</td>
                        <td>
                            <button type="button" class="btn btn-sm btn-outline-danger delete-user-btn"
                                    data-user-id="${user.user_id}" data-user-name="${user.name || 'N/A'}">
                                <i class="bi bi-trash"></i>
                            </button>
                        </td>
                    `;
                });

                userCount.textContent = `${data.total_users} users`;
                deviceUsers.classList.remove('hidden');
                deviceStatus.className = 'alert alert-success';
                deviceStatus.innerHTML = `<i class="bi bi-check-circle"></i> Loaded ${data.total_users} users`;

                // Add delete user event listeners
                document.querySelectorAll('.delete-user-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const userId = this.dataset.userId;
                        const userName = this.dataset.userName;

                        if (confirm(`Delete user ${userId} (${userName}) from device?`)) {
                            deleteUserFromDevice(deviceIP, userId, this);
                        }
                    });
                });

            } else {
                deviceStatus.className = 'alert alert-danger';
                deviceStatus.innerHTML = `<i class="bi bi-x-circle"></i> Failed to load users: ${data.message}`;
            }
        })
        .catch(error => {
            loadUsersBtn.disabled = false;
            loadUsersBtn.innerHTML = '<i class="bi bi-people"></i> Load Users from Device';
            deviceStatus.className = 'alert alert-danger';
            deviceStatus.innerHTML = `<i class="bi bi-x-circle"></i> Error loading users: ${error.message}`;
        });
    });

    // Real-time attendance updates
    let attendanceUpdateInterval;

    function startRealtimeUpdates() {
        // Update every 10 seconds
        attendanceUpdateInterval = setInterval(updateAttendanceTable, 10000);
        console.log('📡 Started real-time attendance updates');
    }

    function stopRealtimeUpdates() {
        if (attendanceUpdateInterval) {
            clearInterval(attendanceUpdateInterval);
            console.log('⏹️ Stopped real-time attendance updates');
        }
    }

    function updateAttendanceTable() {
        fetch('/get_realtime_attendance')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateAttendanceDisplay(data.attendance_data);
                    updateAttendanceSummary(data.summary);

                    // Update last refresh time
                    const now = new Date();
                    const lastUpdateElement = document.getElementById('lastUpdateTime');
                    if (lastUpdateElement) {
                        lastUpdateElement.innerHTML = `<i class="bi bi-check-circle"></i> Last updated: ${now.toLocaleTimeString()}`;
                    }
                    console.log(`🔄 Attendance data updated at ${now.toLocaleTimeString()}`);
                }
            })
            .catch(error => {
                console.error('❌ Failed to update attendance data:', error);
            });
    }

    function updateAttendanceDisplay(attendanceData) {
        const tbody = document.getElementById('attendanceTableBody');
        if (!tbody) return;

        // Update each row with new data
        attendanceData.forEach(attendance => {
            const row = tbody.querySelector(`tr[data-staff-id="${attendance.staff_id}"]`);
            if (row) {
                // Update timing columns
                const timeInCell = row.querySelector('.time-in');
                const timeOutCell = row.querySelector('.time-out');
                const overtimeInCell = row.querySelector('.overtime-in');
                const overtimeOutCell = row.querySelector('.overtime-out');
                const statusCell = row.querySelector('.status');

                if (timeInCell) timeInCell.textContent = attendance.time_in || '--:--:--';
                if (timeOutCell) timeOutCell.textContent = attendance.time_out || '--:--:--';
                if (overtimeInCell) overtimeInCell.textContent = attendance.overtime_in || '--:--:--';
                if (overtimeOutCell) overtimeOutCell.textContent = attendance.overtime_out || '--:--:--';

                // Update status badge
                if (statusCell) {
                    let badgeClass = 'bg-secondary';
                    let statusText = 'Not Marked';

                    switch (attendance.status) {
                        case 'present':
                            badgeClass = 'bg-success';
                            statusText = 'Present';
                            break;
                        case 'late':
                            badgeClass = 'bg-warning';
                            statusText = 'Late';
                            break;
                        case 'absent':
                            badgeClass = 'bg-danger';
                            statusText = 'Absent';
                            break;
                        case 'leave':
                            badgeClass = 'bg-info';
                            statusText = 'On Leave';
                            break;
                    }

                    statusCell.innerHTML = `<span class="badge ${badgeClass}">${statusText}</span>`;
                }

                // Add visual feedback for recent updates
                if (attendance.time_in || attendance.time_out || attendance.overtime_in || attendance.overtime_out) {
                    // Check if this is a new update by comparing with stored data
                    const currentData = row.dataset;
                    let hasNewData = false;

                    if (currentData.timeIn !== (attendance.time_in || '') ||
                        currentData.timeOut !== (attendance.time_out || '') ||
                        currentData.overtimeIn !== (attendance.overtime_in || '') ||
                        currentData.overtimeOut !== (attendance.overtime_out || '')) {
                        hasNewData = true;
                    }

                    // Store current data for next comparison
                    row.dataset.timeIn = attendance.time_in || '';
                    row.dataset.timeOut = attendance.time_out || '';
                    row.dataset.overtimeIn = attendance.overtime_in || '';
                    row.dataset.overtimeOut = attendance.overtime_out || '';

                    if (hasNewData) {
                        // Highlight row for new updates
                        row.style.backgroundColor = '#d4edda';
                        row.style.transition = 'background-color 0.3s ease';

                        // Show notification
                        showNotification(`${attendance.full_name} updated attendance`, 'success');

                        setTimeout(() => {
                            row.style.backgroundColor = '';
                        }, 3000);
                    }
                }
            }
        });
    }

    function updateAttendanceSummary(summary) {
        // Update summary badges in header
        const presentBadge = document.querySelector('.badge.bg-success');
        const absentBadge = document.querySelector('.badge.bg-danger');
        const lateBadge = document.querySelector('.badge.bg-warning');
        const leaveBadge = document.querySelector('.badge.bg-info');

        if (presentBadge) presentBadge.textContent = `${summary.present || 0} Present`;
        if (absentBadge) absentBadge.textContent = `${summary.absent || 0} Absent`;
        if (lateBadge) lateBadge.textContent = `${summary.late || 0} Late`;
        if (leaveBadge) leaveBadge.textContent = `${summary.on_leave || 0} On Leave`;
    }

    function showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'success' ? 'success' : 'info'} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            <i class="bi bi-${type === 'success' ? 'check-circle' : 'info-circle'}"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // Add to page
        document.body.appendChild(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    // Start real-time updates when page loads
    document.addEventListener('DOMContentLoaded', function() {
        startRealtimeUpdates();

        // Stop updates when page is hidden/minimized
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                stopRealtimeUpdates();
            } else {
                startRealtimeUpdates();
            }
        });

        // Stop updates when page unloads
        window.addEventListener('beforeunload', function() {
            stopRealtimeUpdates();
        });
    });

    // User search functionality
    document.getElementById('userSearch')?.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const rows = document.querySelectorAll('#usersTableBody tr');

        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                row.classList.remove('hidden');
            } else {
                row.classList.add('hidden');
            }
        });
    });

    // Helper function to delete user from device
    function deleteUserFromDevice(deviceIP, userId, buttonElement) {
        buttonElement.disabled = true;
        buttonElement.innerHTML = '<i class="bi bi-hourglass-split"></i>';

        fetch('/delete_biometric_user', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: `device_ip=${encodeURIComponent(deviceIP)}&user_id=${encodeURIComponent(userId)}&csrf_token=${encodeURIComponent(getCSRFToken())}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Remove the row from table
                buttonElement.closest('tr').remove();

                // Update user count
                const userCount = document.getElementById('userCount');
                const currentCount = parseInt(userCount.textContent);
                userCount.textContent = `${currentCount - 1} users`;

                deviceStatus.className = 'alert alert-success';
                deviceStatus.innerHTML = `<i class="bi bi-check-circle"></i> User ${userId} deleted successfully`;
            } else {
                buttonElement.disabled = false;
                buttonElement.innerHTML = '<i class="bi bi-trash"></i>';
                deviceStatus.className = 'alert alert-danger';
                deviceStatus.innerHTML = `<i class="bi bi-x-circle"></i> Failed to delete user: ${data.message}`;
            }
        })
        .catch(error => {
            buttonElement.disabled = false;
            buttonElement.innerHTML = '<i class="bi bi-trash"></i>';
            deviceStatus.className = 'alert alert-danger';
            deviceStatus.innerHTML = `<i class="bi bi-x-circle"></i> Error deleting user: ${error.message}`;
        });
    }
});

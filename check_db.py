#!/usr/bin/env python3
import sqlite3
import os

def check_database():
    db_path = 'attendance.db'
    
    if not os.path.exists(db_path):
        print(f"Database {db_path} does not exist")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Get all tables
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    print(f"Tables in database: {[t[0] for t in tables]}")
    
    # Check attendance table if it exists
    if any('attendance' in t for t in tables):
        cursor.execute("PRAGMA table_info(attendance)")
        columns = cursor.fetchall()
        print(f"Attendance table columns: {[col[1] for col in columns]}")
    
    conn.close()

if __name__ == '__main__':
    check_database()
